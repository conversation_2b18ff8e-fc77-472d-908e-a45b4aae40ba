{"name": "candy-cal-be", "version": "0.0.1", "description": "A NestJS project", "main": "dist/main.js", "scripts": {"start": "nest start", "start:dev": "nest start --watch", "build": "tsc", "start:prod": "node dist/main"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.12", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/swagger": "^11.0.6", "@nestjs/websockets": "^11.1.3", "body-parser": "^1.20.3", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "lodash": "^4.17.21", "mongodb": "^6.14.2", "mongoose": "^8.12.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "socket.io": "^4.8.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.1", "@types/lodash": "^4.17.17", "@types/multer": "^1.4.12", "@types/passport-jwt": "^4.0.1", "prettier": "^3.5.3", "typescript": "^5.0.0"}}